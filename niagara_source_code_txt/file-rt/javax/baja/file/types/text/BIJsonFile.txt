/*
 * Copyright 2017 Tridium, Inc. All Rights Reserved.
 */
package javax.baja.file.types.text;

import javax.baja.nre.annotations.NiagaraType;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;

/**
 * BIJsonFile should be implemented by json files.
 *
 * <AUTHOR>
 * @creation  31 Jan 17
 * @since     Niagara 4.4
 */
@NiagaraType
public interface BIJsonFile
  extends BITextFile
{
//region /*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
//@formatter:off
/*@ $javax.baja.file.types.text.BIJsonFile(2979906276)1.0$ @*/
/* Generated Fri Jan 14 17:44:42 CST 2022 by Slot-o-Matic (c) Tridium, Inc. 2012-2022 */

  //region Type

  Type TYPE = Sys.loadType(BIJsonFile.class);

  //endregion Type

//@formatter:on
//endregion /*+ ------------ E<PERSON> BAJA AUTO GENERATED CODE -------------- +*/
}
